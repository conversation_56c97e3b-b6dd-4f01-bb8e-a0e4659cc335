# Liam.Logging

现代化日志记录功能库，支持多级日志、多种输出目标、结构化日志、异步记录、日志轮转等功能，完全兼容Microsoft.Extensions.Logging。

## 功能特性

### 核心功能
- ✅ **多级日志记录**: 支持Trace、Debug、Info、Warning、Error、Critical等标准日志级别
- ✅ **多种输出目标**: 控制台、文件、数据库、网络等多种日志输出方式
- ✅ **结构化日志**: 支持结构化日志记录，便于日志分析和查询
- ✅ **异步日志**: 提供高性能的异步日志记录功能，避免阻塞主线程
- ✅ **日志格式化**: 支持Text和JSON格式，可自定义日志格式和模板
- ✅ **日志过滤**: 基于级别、类别、条件的日志过滤机制

### 高级功能
- ✅ **日志轮转**: 文件大小和时间基础的日志文件轮转
- ✅ **缓冲机制**: 批量写入优化性能
- ✅ **依赖注入**: 完整的DI容器支持和配置
- ✅ **配置管理**: 支持appsettings.json、环境变量等配置方式
- ✅ **性能监控**: 日志记录性能指标和监控
- ✅ **线程安全**: 确保多线程环境下的安全性
- ✅ **作用域支持**: 支持日志作用域，便于跟踪请求上下文

## 安装

```bash
dotnet add package Liam.Logging
```

## 快速开始

### 基本使用

```csharp
using Liam.Logging.Extensions;
using Liam.Logging.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

// 配置服务
var builder = Host.CreateApplicationBuilder(args);

builder.Services.AddLiamLogging()
    .AddConsoleLogging()
    .AddFileLogging(config =>
    {
        config.FilePath = "logs/app.log";
        config.EnableRotation = true;
        config.MaxFileSize = 10 * 1024 * 1024; // 10MB
    });

var host = builder.Build();

// 使用日志记录器
var logger = host.Services.GetRequiredService<ILiamLogger<Program>>();

logger.LogInformation("应用程序启动");
logger.LogWarning("这是一个警告消息");
logger.LogError("发生了错误", new Exception("示例异常"));

await host.RunAsync();
```

### 结构化日志

```csharp
// 结构化日志记录
logger.LogInformationStructured("用户 {UserId} 执行了操作 {Action}", 123, "登录");
logger.LogErrorStructured("处理订单 {OrderId} 时发生错误: {ErrorMessage}", 
    orderId, ex.Message);

// 使用扩展方法
logger.LogInformationStructured("订单 {OrderId} 状态更新为 {Status}", 
    order.Id, order.Status);
```

### 异步日志记录

```csharp
// 异步日志记录
await logger.LogInformationAsync("异步处理完成");
await logger.LogErrorAsync("异步操作失败", exception);

// 异步结构化日志
await logger.LogStructuredAsync(LogLevel.Information, 
    "处理了 {Count} 个项目，耗时 {Duration}ms", 
    new object[] { count, duration });
```

### 日志作用域

```csharp
// 使用作用域
using (logger.BeginScope("RequestId: {RequestId}", requestId))
{
    logger.LogInformation("开始处理请求");
    
    using (logger.BeginScope("UserId: {UserId}", userId))
    {
        logger.LogInformation("用户验证成功");
        // 这些日志会包含RequestId和UserId信息
    }
    
    logger.LogInformation("请求处理完成");
}

// 使用扩展方法的作用域
await logger.WithScopeAsync($"Operation: {operationName}", async () =>
{
    logger.LogInformation("操作开始");
    await DoSomethingAsync();
    logger.LogInformation("操作完成");
});
```

## 配置

### appsettings.json配置

```json
{
  "Logging": {
    "MinimumLevel": "Information",
    "EnableAsync": true,
    "AsyncQueueSize": 10000,
    "BatchSize": 100,
    "BatchTimeoutMs": 1000,
    "IncludeScopes": true,
    "IncludeExceptionDetails": true,
    "IncludeSourceInfo": false,
    "Providers": [
      {
        "TypeName": "Console",
        "Enabled": true,
        "Settings": {
          "EnableColors": true,
          "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff"
        }
      },
      {
        "TypeName": "File",
        "Enabled": true,
        "Settings": {
          "FilePath": "logs/app.log",
          "EnableRotation": true,
          "MaxFileSize": 10485760,
          "RetainedFileCount": 10,
          "FormatterType": "Text"
        }
      }
    ]
  }
}
```

### 代码配置

```csharp
builder.Services.ConfigureLiamLogging(logging =>
{
    logging
        .SetMinimumLevel(LogLevel.Information)
        .EnableAsync(queueSize: 10000, batchSize: 100)
        .EnablePerformanceMonitoring()
        .EnableScopes()
        .AddConsole(config =>
        {
            config.EnableColors = true;
            config.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff";
        })
        .AddFile(config =>
        {
            config.FilePath = "logs/app.log";
            config.EnableRotation = true;
            config.MaxFileSize = 10 * 1024 * 1024;
            config.RetainedFileCount = 10;
        });
});
```

## 日志提供程序

### 控制台提供程序

```csharp
builder.Services.AddConsoleLogging(config =>
{
    config.EnableColors = true;
    config.UseStandardError = false;
    config.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff";
});
```

### 文件提供程序

```csharp
builder.Services.AddFileLogging(config =>
{
    config.FilePath = "logs/app.log";
    config.EnableRotation = true;
    config.MaxFileSize = 10 * 1024 * 1024; // 10MB
    config.RetainedFileCount = 10;
    config.RotationInterval = TimeSpan.FromDays(1);
    config.Encoding = "UTF-8";
    config.AutoFlush = true;
});
```

## 日志格式化器

### 文本格式化器

```csharp
var formatter = new TextLogFormatter(
    timestampFormat: "yyyy-MM-dd HH:mm:ss.fff",
    includeScopes: true,
    includeExceptionDetails: true,
    includeSourceInfo: false);
```

### JSON格式化器

```csharp
var jsonOptions = new JsonSerializerOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    WriteIndented = false
};
var formatter = new JsonLogFormatter(jsonOptions);
```

## 日志过滤器

### 级别过滤器

```csharp
var levelFilter = new LogLevelFilter(LogLevel.Information, LogLevel.Critical);
```

### 类别过滤器

```csharp
// 白名单模式
var categoryFilter = new LogCategoryFilter(new[] { "MyApp.Controllers", "MyApp.Services" });

// 黑名单模式
var categoryFilter = new LogCategoryFilter(new[] { "Microsoft", "System" }, isBlacklist: true);
```

### 消息过滤器

```csharp
var messageFilter = new LogMessageFilter(
    includePatterns: new[] { @"User \d+ logged in" },
    excludePatterns: new[] { @"Health check" });
```

## 性能监控

```csharp
// 启用性能监控
builder.Services.ConfigureLiamLogging(logging =>
{
    logging.EnablePerformanceMonitoring();
});

// 获取性能统计
var factory = serviceProvider.GetRequiredService<ILiamLoggerFactory>();
var stats = factory.GetPerformanceStats();
```

## 扩展方法

### 方法跟踪

```csharp
public async Task ProcessOrderAsync(int orderId)
{
    logger.LogMethodEntry(new { orderId });
    
    try
    {
        var result = await ProcessOrder(orderId);
        logger.LogMethodExit(new { result });
        return result;
    }
    catch (Exception ex)
    {
        logger.LogError("处理订单失败", ex);
        throw;
    }
}
```

### 性能记录

```csharp
var stopwatch = Stopwatch.StartNew();
await ProcessDataAsync();
stopwatch.Stop();

logger.LogPerformance("数据处理", stopwatch.Elapsed);
```

## 最佳实践

1. **使用结构化日志**: 便于日志分析和查询
2. **合理设置日志级别**: 避免过多的调试日志影响性能
3. **使用异步日志**: 在高并发场景下提升性能
4. **配置日志轮转**: 避免日志文件过大
5. **使用作用域**: 便于跟踪请求上下文
6. **监控日志性能**: 及时发现性能问题

## API文档

### 核心接口

- `ILiamLogger`: 主要的日志记录器接口
- `ILiamLogger<T>`: 泛型日志记录器接口
- `ILiamLoggerFactory`: 日志记录器工厂接口
- `ILogProvider`: 日志提供程序接口
- `ILogFormatter`: 日志格式化器接口
- `ILogFilter`: 日志过滤器接口

### 主要类

- `LiamLogger`: 日志记录器实现
- `LiamLoggerFactory`: 日志记录器工厂实现
- `ConsoleLogProvider`: 控制台日志提供程序
- `FileLogProvider`: 文件日志提供程序
- `TextLogFormatter`: 文本格式化器
- `JsonLogFormatter`: JSON格式化器

## 版本历史

| 版本 | 发布日期 | 主要变更 |
|------|----------|----------|
| 1.0.0 | 2025-06-15 | 初始版本，包含核心日志功能 |

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 贡献

欢迎提交Issue和Pull Request来帮助改进这个项目。

## 支持

如果您在使用过程中遇到问题，请在GitHub上提交Issue。
